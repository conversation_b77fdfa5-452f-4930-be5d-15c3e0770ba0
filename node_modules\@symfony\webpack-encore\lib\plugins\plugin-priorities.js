/*
 * This file is part of the Symfony Webpack Encore package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

'use strict';

module.exports = {
    MiniCssExtractPlugin: 140,
    DeleteUnusedEntriesJSPlugin: 130,
    WebpackManifestPlugin: 120,
    LoaderOptionsPlugin: 110,
    ProvidePlugin: 90,
    DefinePlugin: 70,
    WebpackNotifier: 60,
    VueLoaderPlugin: 50,
    FriendlyErrorsWebpackPlugin: 40,
    AssetOutputDisplayPlugin: 30,
    ForkTsCheckerWebpackPlugin: 10,
    EntryPointsPlugin: -10,
};
