{"name": "@nuxt/friendly-errors-webpack-plugin", "publishConfig": {"access": "public"}, "version": "2.6.0", "description": "Recognizes certain classes of webpack errors and cleans, aggregates and prioritizes them to provide a better Developer Experience", "main": "index.js", "scripts": {"test": "eslint . && jest", "release": "standard-version"}, "files": ["src", "index.js"], "keywords": ["friendly", "errors", "webpack", "plugin"], "author": "<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/nuxt/friendly-errors-webpack-plugin.git"}, "bugs": {"url": "https://github.com/nuxt/friendly-errors-webpack-plugin/issues"}, "license": "MIT", "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0"}, "devDependencies": {"babel-core": "^6.26.3", "babel-eslint": "^8.2.6", "babel-loader": "^7.1.5", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-preset-react": "^6.24.1", "eslint": "^5.16.0", "eslint-config-standard": "^12.0.0", "eslint-loader": "^2.2.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^4.1.0", "expect": "^1.20.2", "jest": "^29.7.0", "memory-fs": "^0.4.1", "standard-version": "^4.4.0", "strip-ansi": "^5.2.0", "webpack": "^4.47.0"}, "dependencies": {"chalk": "^2.4.2", "consola": "^3.2.3", "error-stack-parser": "^2.1.4", "string-width": "^4.2.3"}, "jest": {"testEnvironment": "node", "transformIgnorePatterns": ["/node_modules/", "<rootDir>/src/", "<rootDir>/index.js"]}, "engines": {"node": ">=14.18.0", "npm": ">=5.0.0"}}