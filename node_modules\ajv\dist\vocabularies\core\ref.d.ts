import type { CodeKeywordDefinition } from "../../types";
import type { KeywordCxt } from "../../compile/validate";
import { Code } from "../../compile/codegen";
import { SchemaEnv } from "../../compile";
declare const def: CodeKeywordDefinition;
export declare function getValidate(cxt: KeywordCxt, sch: SchemaEnv): Code;
export declare function callRef(cxt: KeywordCxt, v: Code, sch?: SchemaEnv, $async?: boolean): void;
export default def;
