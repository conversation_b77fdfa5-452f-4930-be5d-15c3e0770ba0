{"name": "@types/resolve", "version": "1.20.2", "description": "TypeScript definitions for resolve", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/resolve", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marionebl", "githubUsername": "marion<PERSON>l"}, {"name": "<PERSON>", "url": "https://github.com/ajafff", "githubUsername": "a<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb", "githubUsername": "lj<PERSON>b"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/resolve"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "f17c01e45c248ed2430e1f5d8b89310de423f1564ee420b04ea91177066c05f1", "typeScriptVersion": "3.9"}