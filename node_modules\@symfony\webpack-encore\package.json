{"name": "@symfony/webpack-encore", "version": "5.1.0", "description": "Webpack Encore is a simpler way to integrate Webpack into your application", "main": "index.js", "scripts": {"test": "yarn run test:main && yarn run test:persistent-cache", "test:main": "mocha --reporter spec test --recursive --ignore test/persistent-cache/*", "test:persistent-cache": "node run-persistent-tests", "lint": "eslint lib test index.js .eslintrc.js --report-unused-disable-directives --max-warnings=0", "travis:lint": "yarn run lint"}, "bin": {"encore": "bin/encore.js"}, "repository": {"type": "git", "url": "git+https://github.com/symfony/webpack-encore.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/symfony/webpack-encore/issues"}, "engines": {"node": "^18.12.0 || ^20.0.0 || >=22.0"}, "homepage": "https://github.com/symfony/webpack-encore", "dependencies": {"@nuxt/friendly-errors-webpack-plugin": "^2.5.1", "babel-loader": "^9.1.3", "css-loader": "^7.1.0", "css-minimizer-webpack-plugin": "^7.0.0", "fastest-levenshtein": "^1.0.16", "mini-css-extract-plugin": "^2.6.0", "picocolors": "^1.1.0", "pretty-error": "^4.0.0", "resolve-url-loader": "^5.0.0", "semver": "^7.3.2", "style-loader": "^3.3.0", "tapable": "^2.2.1", "terser-webpack-plugin": "^5.3.0", "tmp": "^0.2.1", "yargs-parser": "^21.0.0"}, "devDependencies": {"@babel/core": "^7.17.0", "@babel/eslint-parser": "^7.17.0", "@babel/plugin-transform-react-jsx": "^7.12.11", "@babel/preset-env": "^7.16.0", "@babel/preset-react": "^7.9.0", "@babel/preset-typescript": "^7.0.0", "@hotwired/stimulus": "^3.0.0", "@symfony/mock-module": "file:fixtures/stimulus/mock-module", "@symfony/stimulus-bridge": "^3.0.0 || ^4.0.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.0.0", "@vue/babel-plugin-jsx": "^1.0.0", "@vue/babel-preset-jsx": "^1.0.0", "@vue/compiler-sfc": "^3.0.2", "autoprefixer": "^10.2.0", "chai": "^4.2.0", "chai-fs": "^2.0.0", "chai-subset": "^1.6.0", "core-js": "^3.0.0", "eslint": "^8.0.0", "eslint-plugin-header": "^3.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-node": "^11.1.0", "file-loader": "^6.0.0", "fork-ts-checker-webpack-plugin": "^7.0.0 || ^8.0.0 || ^9.0.0", "fs-extra": "^10.0.0", "handlebars": "^4.7.7", "handlebars-loader": "^1.7.0", "http-server": "^14.1.0", "less": "^4.0.0", "less-loader": "^11.0.0 || ^12.2.0", "mocha": "^10.0.0", "postcss": "^8.3.0", "postcss-loader": "^7.0.0 || ^8.1.0", "preact": "^10.5.0", "preact-compat": "^3.17.0", "puppeteer": "^23.2.2", "react": "^18.0.0", "react-dom": "^18.0.0", "sass": "^1.17.0", "sass-loader": "^16.0.1", "sinon": "^14.0.0", "strip-ansi": "^6.0.0", "stylus": "^0.63.0", "stylus-loader": "^7.0.0 || ^8.1.0", "svelte": "^3.50.0 || ^4.2.2", "svelte-loader": "^3.1.0", "ts-loader": "^9.0.0", "typescript": "^5.0.0", "vue": "^3.2.14", "vue-loader": "^17.0.0", "webpack": "^5.72", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0", "webpack-notifier": "^1.15.0"}, "peerDependencies": {"@babel/core": "^7.17.0", "@babel/plugin-transform-react-jsx": "^7.12.11", "@babel/preset-env": "^7.16.0", "@babel/preset-react": "^7.9.0", "@babel/preset-typescript": "^7.0.0", "@symfony/stimulus-bridge": "^3.0.0 || ^4.0.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.0.0", "@vue/babel-plugin-jsx": "^1.0.0", "@vue/babel-preset-jsx": "^1.0.0", "@vue/compiler-sfc": "^2.6 || ^3.0.2", "file-loader": "^6.0.0", "fork-ts-checker-webpack-plugin": "^7.0.0 || ^8.0.0 || ^9.0.0", "handlebars": "^4.7.7", "handlebars-loader": "^1.7.0", "less": "^4.0.0", "less-loader": "^11.0.0 || ^12.2.0", "postcss": "^8.3.0", "postcss-loader": "^7.0.0 || ^8.1.0", "sass": "^1.17.0", "sass-loader": "^16.0.1", "stylus-loader": "^7.0.0 || ^8.1.0", "ts-loader": "^9.0.0", "typescript": "^5.0.0", "vue": "^3.2.14", "vue-loader": "^17.0.0", "webpack": "^5.72", "webpack-cli": "^5.1.4", "webpack-notifier": "^1.15.0"}, "peerDependenciesMeta": {"@babel/core": {"optional": false}, "@babel/plugin-transform-react-jsx": {"optional": true}, "@babel/preset-env": {"optional": false}, "@babel/preset-react": {"optional": true}, "@babel/preset-typescript": {"optional": true}, "@symfony/stimulus-bridge": {"optional": true}, "@vue/babel-helper-vue-jsx-merge-props": {"optional": true}, "@vue/babel-plugin-jsx": {"optional": true}, "@vue/babel-preset-jsx": {"optional": true}, "@vue/compiler-sfc": {"optional": true}, "file-loader": {"optional": true}, "fork-ts-checker-webpack-plugin": {"optional": true}, "handlebars": {"optional": true}, "handlebars-loader": {"optional": true}, "less": {"optional": true}, "less-loader": {"optional": true}, "postcss": {"optional": true}, "postcss-loader": {"optional": true}, "sass": {"optional": true}, "sass-loader": {"optional": true}, "stylus-loader": {"optional": true}, "ts-loader": {"optional": true}, "typescript": {"optional": true}, "vue": {"optional": true}, "vue-loader": {"optional": true}, "webpack": {"optional": false}, "webpack-cli": {"optional": false}, "webpack-dev-server": {"optional": true}, "webpack-notifier": {"optional": true}}, "files": ["lib/", "bin/", "index.js"], "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}