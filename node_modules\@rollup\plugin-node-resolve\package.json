{"name": "@rollup/plugin-node-resolve", "version": "15.3.1", "publishConfig": {"access": "public"}, "description": "Locate and bundle third-party dependencies in node_modules", "license": "MIT", "repository": {"url": "rollup/plugins", "directory": "packages/node-resolve"}, "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/rollup/plugins/tree/master/packages/node-resolve/#readme", "bugs": "https://github.com/rollup/plugins/issues", "main": "./dist/cjs/index.js", "module": "./dist/es/index.js", "exports": {"types": "./types/index.d.ts", "import": "./dist/es/index.js", "default": "./dist/cjs/index.js"}, "engines": {"node": ">=14.0.0"}, "files": ["dist", "!dist/**/*.map", "types", "README.md", "LICENSE"], "keywords": ["rollup", "plugin", "es2015", "npm", "modules"], "peerDependencies": {"rollup": "^2.78.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}, "dependencies": {"@rollup/pluginutils": "^5.0.1", "@types/resolve": "1.20.2", "deepmerge": "^4.2.2", "is-module": "^1.0.0", "resolve": "^1.22.1"}, "devDependencies": {"@babel/core": "^7.19.1", "@babel/plugin-transform-typescript": "^7.10.5", "@rollup/plugin-babel": "^6.0.0", "@rollup/plugin-commonjs": "^23.0.0", "@rollup/plugin-json": "^5.0.0", "es5-ext": "^0.10.62", "rollup": "^4.0.0-24", "source-map": "^0.7.4", "string-capitalize": "^1.0.1"}, "types": "./types/index.d.ts", "ava": {"workerThreads": false, "files": ["!**/fixtures/**", "!**/helpers/**", "!**/recipes/**", "!**/types.ts"]}, "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm build && pnpm lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm test -- --verbose", "prebuild": "del-cli dist", "prerelease": "pnpm build", "pretest": "pnpm build", "release": "pnpm --workspace-root package:release $(pwd)", "test": "pnpm test:ts && ava", "test:ts": "tsc types/index.d.ts test/types.ts --noEmit"}}