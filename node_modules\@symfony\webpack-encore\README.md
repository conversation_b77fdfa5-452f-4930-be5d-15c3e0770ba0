# Webpack Encore: A Simple & Powerful Webpack API

[![Build Status](https://travis-ci.org/symfony/webpack-encore.svg?branch=main)](https://travis-ci.org/symfony/webpack-encore)
[![NPM Version](https://badge.fury.io/js/%40symfony%2Fwebpack-encore.svg)](https://badge.fury.io/js/%40symfony%2Fwebpack-encore)
![Node](https://img.shields.io/node/v/@symfony/webpack-encore.svg)

Webpack Encore is a simpler way to integrate [Webpack](https://webpack.js.org/) into your
application. It *wraps* Webpack, giving you a clean & powerful API
for bundling JavaScript modules, pre-processing CSS & JS and compiling
and minifying assets. Encore gives you a professional asset system
that's a *delight* to use.

> [!TIP]
> Symfony released an [AssetMapper](https://symfony.com/doc/current/frontend/asset_mapper.html) component, a production-ready simpler alternative to Webpack Encore
> that runs entirely in PHP.

Encore is inspired by [Webpacker](https://github.com/rails/webpacker) and
[Mix](https://laravel.com/docs/mix), but stays in the spirit of
Webpack: using its features, concepts and naming conventions for a familiar
feel. It aims to solve the most common Webpack use cases.

> Encore is made by Symfony and works *beautifully* in Symfony applications.
> But it can easily be used in any application... in any language!

## Documentation

[Read the Documentation on symfony.com](https://symfony.com/doc/current/frontend/encore/index.html).
