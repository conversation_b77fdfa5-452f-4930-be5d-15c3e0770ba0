/*
 * This file is part of the Symfony Webpack Encore package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

'use strict';

/**
 * @import WebpackConfig from '../WebpackConfig'
 */

/**
 * @import FriendlyErrorsWebpackPlugin from '@nuxt/friendly-errors-webpack-plugin'
 */

const pathUtil = require('../config/path-util');
const AssetOutputDisplayPlugin = require('../friendly-errors/asset-output-display-plugin');
const PluginPriorities = require('./plugin-priorities');

/**
 * Updates plugins array passed adding AssetOutputDisplayPlugin instance
 *
 * @param {Array} plugins
 * @param {WebpackConfig} webpackConfig
 * @param {FriendlyErrorsWebpackPlugin} friendlyErrorsPlugin
 * @returns {void}
 */
module.exports = function(plugins, webpackConfig, friendlyErrorsPlugin) {
    if (webpackConfig.useDevServer()) {
        return;
    }

    const outputPath = pathUtil.getRelativeOutputPath(webpackConfig);
    plugins.push({
        plugin: new AssetOutputDisplayPlugin(outputPath, friendlyErrorsPlugin),
        priority: PluginPriorities.AssetOutputDisplayPlugin
    });
};
